
## Solution Implementation

import logging
from decimal import Decimal, InvalidOperation, ROUND_HALF_UP
from datetime import datetime, timezone
from uuid import uuid4
from flask import Flask, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from sqlalchemy.exc import SQLAlchemyError
from database import db, Account, Transfer

app = Flask(__name__)

# Monetary precision constant
CENT = Decimal("0.01")

# Custom exceptions for better error handling
class TransferError(Exception):
    """Base exception for transfer operations."""
    pass

class AccountNotFound(TransferError):
    """Raised when an account cannot be found."""
    pass

class InsufficientFunds(TransferError):
    """Raised when source account has insufficient balance."""
    pass

class DuplicateTransfer(TransferError):
    """Raised when attempting to process a duplicate transfer."""
    pass

class InvalidTransferData(TransferError):
    """Raised when transfer data is invalid."""
    pass

@app.route("/api/transfers/execute", methods=["POST"])
@jwt_required()
def execute_transfer():
    """
    Execute a secure funds transfer between accounts.
    
    Fixes all identified security vulnerabilities:
    - Proper authorization checks (BOLA/IDOR prevention)
    - Input validation and sanitization
    - SQL injection prevention using ORM
    - Atomic transactions with row-level locking
    - Proper error handling without information disclosure
    - Idempotency protection
    """
    current_profile_id = get_jwt_identity()
    logger = logging.getLogger(__name__)
    
    try:
        # Validate request data exists
        data = request.get_json()
        if not data:
            return jsonify({"error": "Invalid JSON data"}), 400
        
        # Extract and validate required fields
        source_account_id = data.get("source_account_id")
        destination_account_id = data.get("destination_account_id")
        raw_amount = data.get("amount")
        description = data.get("description", "")
        
        # Get idempotency key from header or generate one
        idempotency_key = request.headers.get("Idempotency-Key") or str(uuid4())
        
        # Validate required fields
        if not source_account_id:
            return jsonify({"error": "Source account ID is required"}), 400
        if not destination_account_id:
            return jsonify({"error": "Destination account ID is required"}), 400
        if raw_amount is None:
            return jsonify({"error": "Amount is required"}), 400
        
        # Validate and quantize amount to prevent precision issues
        try:
            amount = Decimal(str(raw_amount)).quantize(CENT, ROUND_HALF_UP)
            if amount <= 0:
                return jsonify({"error": "Amount must be positive"}), 400
        except (InvalidOperation, ValueError):
            return jsonify({"error": "Invalid amount format"}), 400
        
        # Validate accounts are different
        if source_account_id == destination_account_id:
            return jsonify({"error": "Source and destination accounts must be different"}), 400
        
        # Start atomic transaction with proper error handling
        try:
            with db.session.begin():
                # Check for duplicate transfer (idempotency)
                existing_transfer = (
                    db.session.query(Transfer)
                    .filter_by(idempotency_key=idempotency_key)
                    .first()
                )
                if existing_transfer:
                    return jsonify({
                        "transaction_id": existing_transfer.id,
                        "status": "COMPLETED",
                        "message": "Transfer already processed"
                    }), 200
                
                # Fetch source account with ownership validation and row lock
                # This single query prevents BOLA/IDOR by including profile_id
                source_account = (
                    db.session.query(Account)
                    .filter_by(id=source_account_id, profile_id=current_profile_id)
                    .with_for_update()
                    .first()
                )
                if not source_account:
                    return jsonify({"error": "Source account not found or not authorized"}), 404
                
                # Fetch destination account with row lock
                destination_account = (
                    db.session.query(Account)
                    .filter_by(id=destination_account_id)
                    .with_for_update()
                    .first()
                )
                if not destination_account:
                    return jsonify({"error": "Destination account not found"}), 404
                
                # Validate sufficient funds
                if source_account.balance < amount:
                    return jsonify({"error": "Insufficient funds"}), 400
                
                # Execute transfer atomically
                timestamp = datetime.now(tz=timezone.utc)
                
                # Update balances
                source_account.balance -= amount
                source_account.last_updated = timestamp
                destination_account.balance += amount
                destination_account.last_updated = timestamp
                
                # Create transfer record for audit trail
                transfer = Transfer(
                    source_account_id=source_account.id,
                    destination_account_id=destination_account.id,
                    amount=amount,
                    description=description,
                    initiated_by=current_profile_id,
                    idempotency_key=idempotency_key,
                    created_at=timestamp,
                    status="COMPLETED"
                )
                db.session.add(transfer)
                
                # Commit happens automatically when exiting the context manager
                
            # Log successful transfer (outside transaction)
            logger.info(
                f"Transfer completed: {amount} from {source_account_id} to {destination_account_id} "
                f"by profile {current_profile_id}"
            )
            
            return jsonify({
                "transaction_id": transfer.id,
                "status": "COMPLETED",
                "amount": str(amount),
                "timestamp": timestamp.isoformat()
            }), 201
            
        except SQLAlchemyError as e:
            # Database errors - log details but return generic message
            logger.error(f"Database error during transfer: {str(e)}", exc_info=True)
            return jsonify({"error": "Transfer failed due to system error"}), 500
            
    except Exception as e:
        # Catch-all for unexpected errors
        logger.error(f"Unexpected error during transfer: {str(e)}", exc_info=True)
        return jsonify({"error": "Internal server error"}), 500

if __name__ == "__main__":
    app.run(debug=False)  # Never run debug=True in production