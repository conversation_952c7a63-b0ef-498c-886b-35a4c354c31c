#!/usr/bin/env python3
"""
Secure Transfer API Implementation
"""

import logging
from decimal import Decimal, InvalidOperation, ROUND_HALF_UP
from datetime import datetime, timezone
from uuid import uuid4
from typing import Optional, Dict, Any
from database import db, Account, Transfer

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Monetary precision constant
CENT = Decimal("0.01")

class TransferError(Exception):
    """Base exception for transfer operations."""
    pass

class AccountNotFound(TransferError):
    """Raised when an account cannot be found."""
    pass

class InsufficientFunds(TransferError):
    """Raised when source account has insufficient balance."""
    pass

class DuplicateTransfer(TransferError):
    """Raised when attempting to process a duplicate transfer."""
    pass

class InvalidTransferData(TransferError):
    """Raised when transfer data is invalid."""
    pass

class TransferService:
    """Service for executing secure money transfers between accounts."""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def execute_transfer(
        self,
        current_profile_id: str,
        source_account_id: str,
        destination_account_id: str,
        raw_amount: str,
        description: str = "",
        idempotency_key: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Execute a funds transfer between accounts.

        Args:
            current_profile_id: Authenticated user's profile ID
            source_account_id: Source account ID
            destination_account_id: Destination account ID
            raw_amount: Transfer amount as string
            description: Transfer description
            idempotency_key: Optional idempotency key for duplicate prevention

        Returns:
            Dict containing transfer result

        Raises:
            TransferError subclasses for different failure scenarios
        """
        try:
            if not idempotency_key:
                idempotency_key = str(uuid4())

            try:
                amount = Decimal(str(raw_amount)).quantize(CENT, ROUND_HALF_UP)
                if amount <= 0:
                    raise InvalidTransferData("Amount must be positive")
            except (InvalidOperation, ValueError):
                raise InvalidTransferData("Invalid amount format")

            if not source_account_id:
                raise InvalidTransferData("Source account ID is required")
            if not destination_account_id:
                raise InvalidTransferData("Destination account ID is required")

            if source_account_id == destination_account_id:
                raise InvalidTransferData("Source and destination accounts must be different")

            with db.session.begin():
                existing_transfer = (
                    db.session.query(Transfer)
                    .filter_by(idempotency_key=idempotency_key)
                    .first()
                )
                if existing_transfer:
                    return {
                        "transaction_id": existing_transfer.id,
                        "status": "COMPLETED",
                        "message": "Transfer already processed"
                    }

                source_account = (
                    db.session.query(Account)
                    .filter_by(id=source_account_id, profile_id=current_profile_id)
                    .with_for_update()
                    .first()
                )
                if not source_account:
                    raise AccountNotFound("Source account not found or not authorized")

                destination_account = (
                    db.session.query(Account)
                    .filter_by(id=destination_account_id)
                    .with_for_update()
                    .first()
                )
                if not destination_account:
                    raise AccountNotFound("Destination account not found")

                if source_account.balance < amount:
                    raise InsufficientFunds("Insufficient funds")

                timestamp = datetime.now(tz=timezone.utc)

                source_account.balance -= amount
                source_account.last_updated = timestamp
                destination_account.balance += amount
                destination_account.last_updated = timestamp

                transfer = Transfer(
                    id=str(uuid4()),
                    source_account_id=source_account.id,
                    destination_account_id=destination_account.id,
                    amount=amount,
                    description=description,
                    initiated_by=current_profile_id,
                    idempotency_key=idempotency_key,
                    created_at=timestamp,
                    status="COMPLETED"
                )
                db.session.add(transfer)

            self.logger.info(
                f"Transfer completed: {amount} from {source_account_id} to {destination_account_id} "
                f"by profile {current_profile_id}"
            )

            return {
                "transaction_id": transfer.id,
                "status": "COMPLETED",
                "amount": str(amount),
                "timestamp": timestamp.isoformat()
            }

        except TransferError as e:
            self.logger.error(f"Transfer failed: {str(e)}")
            raise
        except Exception as e:
            self.logger.error(f"Unexpected error during transfer: {str(e)}", exc_info=True)
            raise TransferError("Transfer failed due to system error") from e

# Initialize sample data
def init_sample_data():
    """Initialize with sample accounts for demonstration."""
    accounts = [
        Account("acc_001", "profile_123", Decimal("1000.00"), datetime.now(timezone.utc)),
        Account("acc_002", "profile_456", Decimal("500.00"), datetime.now(timezone.utc)),
        Account("acc_003", "profile_789", Decimal("0.00"), datetime.now(timezone.utc)),
    ]
    
    for account in accounts:
        db.session.accounts[account.id] = account
    
    logger.info("Sample data initialized")

# Initialize sample data
init_sample_data()

if __name__ == "__main__":
    service = TransferService()

    print("=== Transfer API Demonstration ===")
    print()

    # Example 1: Successful transfer
    try:
        result = service.execute_transfer(
            current_profile_id="profile_123",
            source_account_id="acc_001",
            destination_account_id="acc_003",
            raw_amount="100.00",
            description="Test transfer"
        )
        print(f"✅ Successful transfer: {result}")
    except Exception as e:
        print(f"❌ Transfer failed: {e}")

    print()

    # Example 2: Unauthorized access attempt
    try:
        result = service.execute_transfer(
            current_profile_id="profile_123",
            source_account_id="acc_002",
            destination_account_id="acc_003",
            raw_amount="50.00",
            description="Unauthorized transfer attempt"
        )
        print(f"❌ This should not succeed: {result}")
    except AccountNotFound as e:
        print(f"✅ Unauthorized access prevented: {e}")

    print()

    # Example 3: Invalid amount
    try:
        result = service.execute_transfer(
            current_profile_id="profile_123",
            source_account_id="acc_001",
            destination_account_id="acc_003",
            raw_amount="-50.00",
            description="Invalid amount test"
        )
        print(f"❌ This should not succeed: {result}")
    except InvalidTransferData as e:
        print(f"✅ Invalid amount rejected: {e}")

    print()
    print("=== All validations working correctly! ===")
