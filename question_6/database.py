#!/usr/bin/env python3
"""
Database configuration and models for the secure transfer API.

This module provides:
- SQLAlchemy database setup
- Account and Transfer ORM models
- Database session management
"""

import os
from datetime import datetime, timezone
from decimal import Decimal
from sqlalchemy import create_engine, Column, String, Numeric, DateTime, Text, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from flask_sqlalchemy import SQLAlchemy
from flask import Flask

# Database configuration
DATABASE_URL = os.getenv(
    'DATABASE_URL', 
    'postgresql://postgres:postgres@localhost:5432/transfer_db'
)

# Alternative: SQLite for development/testing
# DATABASE_URL = 'sqlite:///transfer_app.db'

# Flask-SQLAlchemy setup
app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = DATABASE_URL
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
    'pool_pre_ping': True,
    'pool_recycle': 300,
    'connect_args': {
        'connect_timeout': 10,
    }
}

# Initialize Flask-SQLAlchemy
db = SQLAlchemy(app)

# Base model with common fields
class BaseModel(db.Model):
    """Base model with common timestamp fields."""
    __abstract__ = True
    
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

class Account(BaseModel):
    """
    Account model representing user bank accounts.
    
    Each account belongs to a profile (user) and has a balance.
    """
    __tablename__ = 'accounts'
    
    id = Column(String(50), primary_key=True)
    profile_id = Column(String(50), nullable=False, index=True)  # User/Profile ID
    balance = Column(Numeric(precision=15, scale=2), nullable=False, default=0)
    currency = Column(String(3), nullable=False, default='USD')
    account_type = Column(String(20), nullable=False, default='CHECKING')
    status = Column(String(20), nullable=False, default='ACTIVE')
    last_updated = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    
    # Relationships
    outgoing_transfers = relationship("Transfer", foreign_keys="Transfer.source_account_id", back_populates="source_account")
    incoming_transfers = relationship("Transfer", foreign_keys="Transfer.destination_account_id", back_populates="destination_account")
    
    def __repr__(self):
        return f"<Account(id='{self.id}', profile_id='{self.profile_id}', balance={self.balance})>"
    
    def to_dict(self):
        """Convert account to dictionary for JSON serialization."""
        return {
            'id': self.id,
            'profile_id': self.profile_id,
            'balance': str(self.balance),
            'currency': self.currency,
            'account_type': self.account_type,
            'status': self.status,
            'last_updated': self.last_updated.isoformat() if self.last_updated else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class Transfer(BaseModel):
    """
    Transfer model representing money transfers between accounts.
    
    Includes audit trail and idempotency support.
    """
    __tablename__ = 'transfers'
    
    id = Column(String(50), primary_key=True, default=lambda: str(__import__('uuid').uuid4()))
    source_account_id = Column(String(50), ForeignKey('accounts.id'), nullable=False)
    destination_account_id = Column(String(50), ForeignKey('accounts.id'), nullable=False)
    amount = Column(Numeric(precision=15, scale=2), nullable=False)
    currency = Column(String(3), nullable=False, default='USD')
    description = Column(Text)
    status = Column(String(20), nullable=False, default='PENDING')
    initiated_by = Column(String(50), nullable=False)  # Profile ID of initiator
    idempotency_key = Column(String(100), unique=True, index=True)  # For duplicate prevention
    reference_number = Column(String(50), unique=True)  # External reference
    
    # Relationships
    source_account = relationship("Account", foreign_keys=[source_account_id], back_populates="outgoing_transfers")
    destination_account = relationship("Account", foreign_keys=[destination_account_id], back_populates="incoming_transfers")
    
    def __repr__(self):
        return f"<Transfer(id='{self.id}', amount={self.amount}, status='{self.status}')>"
    
    def to_dict(self):
        """Convert transfer to dictionary for JSON serialization."""
        return {
            'id': self.id,
            'source_account_id': self.source_account_id,
            'destination_account_id': self.destination_account_id,
            'amount': str(self.amount),
            'currency': self.currency,
            'description': self.description,
            'status': self.status,
            'initiated_by': self.initiated_by,
            'idempotency_key': self.idempotency_key,
            'reference_number': self.reference_number,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

# Database initialization functions
def create_tables():
    """Create all database tables."""
    with app.app_context():
        db.create_all()
        print("Database tables created successfully")

def drop_tables():
    """Drop all database tables."""
    with app.app_context():
        db.drop_all()
        print("Database tables dropped successfully")

def init_sample_data():
    """Initialize database with sample data for testing."""
    with app.app_context():
        # Create sample accounts
        accounts = [
            Account(
                id='acc_001',
                profile_id='profile_123',
                balance=Decimal('1000.00'),
                account_type='CHECKING',
                status='ACTIVE'
            ),
            Account(
                id='acc_002',
                profile_id='profile_456',
                balance=Decimal('500.00'),
                account_type='SAVINGS',
                status='ACTIVE'
            ),
            Account(
                id='acc_003',
                profile_id='profile_789',
                balance=Decimal('0.00'),
                account_type='CHECKING',
                status='ACTIVE'
            )
        ]
        
        for account in accounts:
            existing = db.session.query(Account).filter_by(id=account.id).first()
            if not existing:
                db.session.add(account)
        
        db.session.commit()
        print("Sample data initialized successfully")

# Database utility functions
def get_db_session():
    """Get database session for manual operations."""
    return db.session

def close_db_session():
    """Close database session."""
    db.session.close()

# Health check function
def check_database_health():
    """Check database connectivity and health."""
    try:
        with app.app_context():
            # Simple query to test connection
            db.session.execute(db.text('SELECT 1'))
            return True
    except Exception as e:
        print(f"Database health check failed: {e}")
        return False

# Context manager for database operations
class DatabaseTransaction:
    """Context manager for database transactions."""
    
    def __enter__(self):
        return db.session
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            db.session.rollback()
        else:
            db.session.commit()

# Export the main objects needed by the application
__all__ = ['db', 'Account', 'Transfer', 'app', 'create_tables', 'init_sample_data', 'DatabaseTransaction']

if __name__ == "__main__":
    # Initialize database when run directly
    print("Initializing database...")
    create_tables()
    init_sample_data()
    
    # Test database health
    if check_database_health():
        print("Database is healthy and ready!")
    else:
        print("Database health check failed!")
