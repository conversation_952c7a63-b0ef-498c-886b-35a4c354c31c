#!/usr/bin/env python3
"""
Database models and session management for the transfer API.
"""

import logging
from decimal import Decimal
from datetime import datetime, timezone
from dataclasses import dataclass
from typing import Optional
from contextlib import contextmanager

logger = logging.getLogger(__name__)

@dataclass
class Account:
    """Account model representing user bank accounts."""
    id: str
    profile_id: str
    balance: Decimal
    last_updated: datetime
    currency: str = "USD"
    status: str = "ACTIVE"

@dataclass
class Transfer:
    """Transfer model for transaction records."""
    id: str
    source_account_id: str
    destination_account_id: str
    amount: Decimal
    description: str
    initiated_by: str
    idempotency_key: str
    created_at: datetime
    status: str = "COMPLETED"

class DatabaseSession:
    """Database session for managing data operations."""
    
    def __init__(self):
        # In real implementation: initialize database connection
        self.accounts = {}
        self.transfers = {}
        self._in_transaction = False
    
    @contextmanager
    def begin(self):
        """Begin database transaction."""
        # In real implementation: BEGIN TRANSACTION
        self._in_transaction = True
        try:
            yield self
            # In real implementation: COMMIT
            logger.debug("Transaction committed")
        except Exception as e:
            # In real implementation: ROLLBACK
            logger.error(f"Transaction rolled back: {e}")
            raise
        finally:
            self._in_transaction = False
    
    def query(self, model_class):
        """Create query for model class."""
        return Query(model_class, self)
    
    def add(self, obj):
        """Add object to session."""
        # In real implementation: session.add(obj)
        if isinstance(obj, Transfer):
            self.transfers[obj.id] = obj
        logger.debug(f"Added {type(obj).__name__} to session")

class Query:
    """Query builder for database operations."""
    
    def __init__(self, model_class, session):
        self.model_class = model_class
        self.session = session
        self._filters = {}
    
    def filter_by(self, **kwargs):
        """Add filter conditions to query."""
        self._filters.update(kwargs)
        return self
    
    def with_for_update(self):
        """Add row-level locking to query."""
        # In real implementation: adds FOR UPDATE clause
        logger.debug("Row-level locking applied")
        return self
    
    def first(self):
        """Execute query and return first result."""
        # In real implementation: execute query and return first result
        if self.model_class == Account:
            for account in self.session.accounts.values():
                if all(getattr(account, k) == v for k, v in self._filters.items()):
                    return account
        elif self.model_class == Transfer:
            for transfer in self.session.transfers.values():
                if all(getattr(transfer, k) == v for k, v in self._filters.items()):
                    return transfer
        return None

class Database:
    """Database interface."""
    def __init__(self):
        self.session = DatabaseSession()

# Global database instance
db = Database()

def init_sample_data():
    """Initialize sample data for demonstration."""
    accounts = [
        Account("acc_001", "profile_123", Decimal("1000.00"), datetime.now(timezone.utc)),
        Account("acc_002", "profile_456", Decimal("500.00"), datetime.now(timezone.utc)),
        Account("acc_003", "profile_789", Decimal("0.00"), datetime.now(timezone.utc)),
    ]
    
    for account in accounts:
        db.session.accounts[account.id] = account
    
    logger.info("Sample data initialized")

# Initialize sample data
init_sample_data()
